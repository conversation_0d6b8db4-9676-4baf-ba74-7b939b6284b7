SALT_WALLET_1 = initial salt (32 bytes) from .env for hashing privatekey wallet
SALT_WALLET_2 = initial salt from input user as DEV on telegram bot
password = keccak256(SALT_WALLET_1 + SALT_WALLET_2)

flow :

generatePrivateKey = random256Bytes() -> 32 bytes without 0x
encryptPrivateKey = encryptText(generatePrivateKey, password) -> encrypted text
decryptPrivateKey = decryptText(encryptPrivateKey, password) -> 32 bytes without 0x

encryptPrivateKey -> store on db
decryptPrivateKey -> use for wallet