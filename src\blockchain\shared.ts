import { getSolanaChainType, formatSolanaChainName, getSolanaCurrencySymbol, getSolanaChainId, isSolanaChain } from "./solana"
import { getEthereumChainType, formatEthereumChainName, getEthereumCurrencySymbol, isEthereumChain } from "./ethereum"
import type { TypeChainName } from "../data"

/**
 * Shared blockchain utilities
 *
 * This module provides generic functions that work across all blockchain networks
 * by delegating to the appropriate blockchain-specific modules.
 */

// ============================================================================
// CORE BLOCKCHAIN UTILITIES - Used functions only
// ============================================================================

/**
 * Get the official chain ID for a given chain type
 * @param chain Chain type
 * @returns Chain ID string as used by the blockchain network
 *
 * @example
 * ```typescript
 * getChainId(staticDataChainList.ETHEREUM_MAINNET) // Returns "1"
 * getChainId(staticDataChainList.SOLANA_MAINNET) // Returns "101"
 * ```
 */
export const getChainId = (chain: TypeChainName): number => {
  if (isSolanaChain(chain)) {
    // return getSolanaChainId(chain)
  }
  if (isEthereumChain(chain)) {
    // return getEthereumChainId(chain)
  }
  return 0
}

/**
 * Format chain name for user-friendly display
 * @param chain Chain type to format
 * @returns Formatted, human-readable chain name
 *
 * @example
 * ```typescript
 * formatChainName(staticDataChainList.SOLANA_MAINNET) // Returns "Solana Mainnet"
 * formatChainName(staticDataChainList.ETHEREUM_TESTNET) // Returns "Ethereum Testnet"
 * ```
 */
export const formatChainName = (chain: TypeChainName): string => {
  if (isSolanaChain(chain)) {
    return formatSolanaChainName(chain)
  }
  if (isEthereumChain(chain)) {
    return formatEthereumChainName(chain)
  }
  return chain
}

/**
 * Get the currency symbol for a blockchain network
 * @param chain Chain type
 * @returns Currency symbol string
 *
 * @example
 * ```typescript
 * getCurrencySymbol(staticDataChainList.SOLANA_MAINNET) // Returns "SOL"
 * getCurrencySymbol(staticDataChainList.ETHEREUM_MAINNET) // Returns "ETH"
 * ```
 */
export const getCurrencySymbol = (chain: TypeChainName): string => {
  if (isSolanaChain(chain)) {
    return getSolanaCurrencySymbol(chain)
  }
  if (isEthereumChain(chain)) {
    return getEthereumCurrencySymbol(chain)
  }
  return "TOKEN"
}

// ============================================================================
// FEE CALCULATION - Uniswap V2 inspired approach
// ============================================================================

/**
 * Calculate fee deduction using your specified mechanism
 * Formula: result = (input * (10000 - calcFee)) / 10000
 * Where calcFee = getFee * 100
 *
 * @param amount Original amount (in smallest unit, e.g., lamports, wei, satoshis)
 * @param feePercentage Fee percentage as string (e.g., "0.3" for 0.3%, "1.5" for 1.5%)
 * @returns Object with amountAfterFee and feeAmount
 *
 * @example
 * ```typescript
 * // Example 1: 0.3% fee on 1 * 10^18 units
 * const input = BigInt(1) * BigInt(10 ** 18)
 * const result = calculateFeeDeduction(input, "0.3")
 * // Formula: (input * (10000 - 30)) / 10000 = (input * 9970) / 10000
 *
 * // Example 2: 0.3% fee on 10,000 units
 * const result2 = calculateFeeDeduction(BigInt(10000), "0.3")
 * // Returns: { amountAfterFee: 9970n, feeAmount: 30n }
 * ```
 */
export const calculateFeeDeduction = (amount: bigint, feePercentage: string | number): { amountAfterFee: bigint; feeAmount: bigint } => {
  if (amount <= 0n) {
    return { amountAfterFee: 0n, feeAmount: 0n }
  }

  // Parse fee percentage
  let getFee: number
  if (typeof feePercentage === "string") {
    getFee = parseFloat(feePercentage)
  } else {
    getFee = feePercentage
  }

  // Validate fee range
  if (isNaN(getFee) || getFee < 0 || getFee > 100) {
    return { amountAfterFee: amount, feeAmount: 0n }
  }

  if (getFee === 0) {
    return { amountAfterFee: amount, feeAmount: 0n }
  }

  // Your specified calculation mechanism:
  // const getFee = 0.3; // from db
  // const calcFee = getFee * 100;
  // const input = 1 * 10 ** 18; // amount input trade
  // const result = (input * (10000 - calcFee)) / 10000;

  const calcFee = Math.floor(getFee * 100) // Convert to basis points (0.3 -> 30)
  const amountAfterFee = (amount * BigInt(10000 - calcFee)) / BigInt(10000)
  const feeAmount = amount - amountAfterFee

  return { amountAfterFee, feeAmount }
}

/**
 * Calculate the amount needed to receive a specific amount after fee deduction
 * This is the inverse of calculateFeeDeduction using your specified mechanism
 *
 * @param desiredAmount Amount you want to receive after fees
 * @param feePercentage Fee percentage as string (e.g., "0.3" for 0.3%, "1.5" for 1.5%)
 * @returns Object with requiredAmount and feeAmount
 *
 * @example
 * ```typescript
 * // Example: How much do I need to send to receive 9,970 after 0.3% fee?
 * const result = calculateRequiredAmount(BigInt(9970), "0.3")
 * // Returns: { requiredAmount: 10000n, feeAmount: 30n }
 * ```
 */
export const calculateRequiredAmount = (desiredAmount: bigint, feePercentage: string | number): { requiredAmount: bigint; feeAmount: bigint } => {
  if (desiredAmount <= 0n) {
    return { requiredAmount: 0n, feeAmount: 0n }
  }

  // Parse fee percentage
  let getFee: number
  if (typeof feePercentage === "string") {
    getFee = parseFloat(feePercentage)
  } else {
    getFee = feePercentage
  }

  // Validate fee range
  if (isNaN(getFee) || getFee < 0 || getFee > 100) {
    return { requiredAmount: desiredAmount, feeAmount: 0n }
  }

  if (getFee === 0) {
    return { requiredAmount: desiredAmount, feeAmount: 0n }
  }

  // Inverse calculation: if result = (input * (10000 - calcFee)) / 10000
  // Then: input = (result * 10000) / (10000 - calcFee)
  const calcFee = Math.floor(getFee * 100) // Convert to basis points (0.3 -> 30)
  const requiredAmount = (desiredAmount * BigInt(10000)) / BigInt(10000 - calcFee)
  const feeAmount = requiredAmount - desiredAmount

  return { requiredAmount, feeAmount }
}

/**
 * Validate fee percentage and convert to basis points
 * @param feePercentage Fee percentage as string or number (0-100)
 * @returns Basis points (0-10000) or null if invalid
 *
 * @example
 * ```typescript
 * validateFeePercentage("0.3")  // Returns 30
 * validateFeePercentage("1.5")  // Returns 150
 * validateFeePercentage("100")  // Returns 10000
 * validateFeePercentage("-1")   // Returns null
 * validateFeePercentage("101")  // Returns null
 * validateFeePercentage(0.3)    // Returns 30
 * ```
 */
export const validateFeePercentage = (feePercentage: string | number): number | null => {
  let fee: number

  if (typeof feePercentage === "string") {
    fee = parseFloat(feePercentage)
  } else if (typeof feePercentage === "number") {
    fee = feePercentage
  } else {
    return null
  }

  if (isNaN(fee) || fee < 0 || fee > 100) {
    return null
  }

  return Math.floor(fee * 100)
}

/**
 * Format fee amount for display with proper decimal places
 * @param feeAmount Fee amount in smallest unit
 * @param decimals Number of decimal places for the token
 * @returns Formatted string
 *
 * @example
 * ```typescript
 * formatFeeAmount(BigInt(30), 4)     // Returns "0.0030"
 * formatFeeAmount(BigInt(15000), 6)  // Returns "0.015000"
 * ```
 */
export const formatFeeAmount = (feeAmount: bigint, decimals: number): string => {
  const divisor = BigInt(10 ** decimals)
  const wholePart = feeAmount / divisor
  const fractionalPart = feeAmount % divisor

  const fractionalStr = fractionalPart.toString().padStart(decimals, "0")
  return `${wholePart}.${fractionalStr}`
}
