import bs58 from "bs58"
import { Wallet, isAddress } from "ethers"
import { Keypair, PublicKey } from "@solana/web3.js"
import { decryptText, encryptText } from "safe-my-text"
import type { TypeChainName } from "../data"

/**
 * Blockchain wallet utilities class
 */
export class BlockchainWallet {
  constructor(private chain: TypeChainName) {}

  /**
   * Check if an address is a valid blockchain address for a given chain type
   * @param address Address string to check
   * @returns True if address is a valid blockchain address for the given chain type
   *
   * @example
   * ```typescript
   * BlockchainWallet.isAddress("******************************************") // Returns true
   * BlockchainWallet.isAddress("invalid") // Returns false
   * ```
   */
  public isAddress(address: string) {
    if (this.chain.startsWith(`ethereum`)) {
      return isAddress(address)
    }
    if (this.chain.startsWith(`solana`)) {
      try {
        new PublicKey(address)
        return true
      } catch (err) {
        return false
      }
    }
    return false
  }

  /**
   * Get address from privatekey
   * @param privateKey Privatekey to get address from
   * @returns Address string
   *
   * @example
   * ```typescript
   * BlockchainWallet.getAddress("3ac225168df54212a25c1c01fd35bebfea408fdac2e31ddd6f80a4bbf9a5f1cb") // Returns "0x..."
   * ```
   */
  public async getAddress(privateKey: string) {
    if (this.chain.startsWith(`solana`)) {
      const decode = bs58.decode(privateKey)
      const { publicKey } = Keypair.fromSecretKey(decode)
      return publicKey.toBase58()
    }
    if (this.chain.startsWith(`ethereum`)) {
      return new Wallet(privateKey).address
    }
    return ``
  }

  /**
   * Generate an Blockchain wallet from random privatekey
   * @returns Generated privatekey and address wallet
   *
   * @example
   * ```typescript
   * BlockchainWallet.generate() // Returns { address: "...", privateKey: "..." }
   * ```
   */
  public generate() {
    const res = { address: ``, privateKey: `` }
    if (this.chain.startsWith(`solana`)) {
      const { publicKey, secretKey } = Keypair.generate()
      Object.assign(res, {
        address: publicKey.toBase58(),
        privateKey: bs58.encode(secretKey)
      })
    }
    if (this.chain.startsWith(`ethereum`)) {
      const { address, privateKey } = Wallet.createRandom()
      Object.assign(res, { address, privateKey })
    }
    return res
  }

  /**
   * Encrypt a privatekey with a password
   * @param privateKey Privatekey to encrypt
   * @param password Password to encrypt with
   * @returns Encrypted privatekey
   *
   * @example
   * ```typescript
   * BlockchainWallet.encrypt("3ac225168df54212a25c1c01fd35bebfea408fdac2e31ddd6f80a4bbf9a5f1cb", "password123") // Returns "U2F.."
   * ```
   */
  public encrypt(privateKey: string, password: string) {
    return encryptText(privateKey, password) as string
  }

  /**
   * Decrypt a privatekey with a password
   * @param chiper Encrypted privatekey
   * @param password Password to decrypt with
   * @returns Decrypted privatekey
   *
   * @example
   * ```typescript
   * BlockchainWallet.decrypt("U2FsdGVkX18...", "password123") // Returns "..."
   * ```
   */
  public decrypt(chipher: string, password: string) {
    return decryptText(chipher, password) as string
  }
}
