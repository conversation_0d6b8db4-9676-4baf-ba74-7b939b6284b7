/**
 * Database connection configuration
 *
 * This file sets up the database connection and exports the database instance
 * along with schema tables for use throughout the application.
 */

import postgres from "postgres"
import { PGlite } from "@electric-sql/pglite"
import { drizzle } from "drizzle-orm/postgres-js"
import { drizzle as drizzlePgLite } from "drizzle-orm/pglite"
import { tableUsers } from "./schema/users"
import { tableWallets } from "./schema/wallets"
import { tradingHistory } from "./schema/trading_history"
import { tableFees } from "./schema/fees"

export const db =
  process.env.NODE_ENV == `production`
    ? drizzle(postgres(process.env.DATABASE_URI || `postgres://postgres:postgres@localhost:5432/postgres`, { prepare: false }), {
        schema: {
          users: tableUsers,
          wallets: tableWallets,
          tradingHistory,
          fees: tableFees
        },
        casing: "snake_case"
      })
    : drizzlePgLite(new PGlite(`memory://`), {
        schema: {
          users: tableUsers,
          wallets: tableWallets,
          tradingHistory,
          fees: tableFees
        },
        casing: "snake_case"
      })
