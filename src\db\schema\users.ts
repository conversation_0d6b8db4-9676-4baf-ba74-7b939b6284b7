/**
 * User schema definition
 *
 * This file defines the users table schema.
 * The primary key is the Telegram user ID.
 * This table stores user information and preferences, while wallet-specific
 * data is stored in the wallets table.
 */

import { sql } from "drizzle-orm"
import { boolean, index, integer, json, pgEnum, pgTable, timestamp, varchar } from "drizzle-orm/pg-core"

/**
 * Type definition for user statistics
 * Stores aggregated user statistics across all networks
 */
export type TypeUserStats = {
  language: string
  networks: Record<
    string, // Chain type
    {
      totalTrades: number
      successTrades: number
      failedTrades: number
      totalProfit: string // Stored as string to preserve precision
    }
  >
}

/**
 * User role enum
 */
export const UserRole = { USER: "user", ADMIN: "admin" } as const
export type TypeUserRole = (typeof UserRole)[keyof typeof UserRole]

export const userRoleEnum = pgEnum("user_role", [UserRole.USER, UserRole.ADMIN])

export const tableUsers = pgTable(
  "users",
  {
    id: integer().primary<PERSON>ey(), // Telegram user ID as primary key
    username: varchar({ length: 32 }).notNull().default(``), // Telegram username
    fullname: varchar({ length: 255 }).notNull().default(``), // Telegram user fullname
    role: userRoleEnum().notNull().default(UserRole.USER), // User role
    isActive: boolean().notNull().default(true), // User status
    fee: varchar({ length: 10 }).notNull().default("0"), // Fee percentage for trading (e.g., "0.3" for 0.3%)
    stats: json().$type<TypeUserStats>().notNull().default({
      // User statistics (aggregated from wallets)
      language: "en",
      networks: {}
    }),
    updatedAt: timestamp()
      .default(sql`CURRENT_TIMESTAMP`)
      .$onUpdate(() => new Date()),
    createdAt: timestamp().default(sql`CURRENT_TIMESTAMP`)
  },
  (table) => [
    // Add index on username for faster searches
    index("user_username_idx").on(table.username),
    // Add index on isActive for faster filtering
    index("user_is_active_idx").on(table.isActive)
  ]
)
