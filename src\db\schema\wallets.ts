/**
 * Wallets table schema
 *
 * This file defines the wallets table schema.
 * The primary key is an auto-incrementing ID.
 * The owner is a foreign key to the users table.
 * Wallet data is stored in JSON fields for flexibility.
 */
import { sql } from "drizzle-orm"
import { bigint, integer, pgEnum, pgTable, serial, timestamp, varchar } from "drizzle-orm/pg-core"
import { tableUsers } from "./users"
import { dataChainName } from "../../data"

export const chainTypeEnum = pgEnum("chain_type", dataChainName as [string, ...string[]])
export const createdByEnum = pgEnum("created_by", ["system", "import"])
export const tableWallets = pgTable("wallets", {
  id: serial().primaryKey(), // Wallet ID
  owner: integer()
    .notNull()
    .references(() => tableUsers.id),
  name: varchar({ length: 32 }).notNull(), // Name of the wallet (max 32 bytes)
  chain: chainTypeEnum().notNull(), // Chain type
  chainId: integer().notNull().default(0), // Chain-specific ID
  address: varchar({ length: 60 }).notNull(), // Address of the wallet (max 60 bytes)
  balance: bigint({ mode: "bigint" }).notNull().default(0n), // Balance of the wallet
  privateKey: varchar({ length: 200 }).notNull(), // Encrypted private key of the wallet (max 200 bytes)
  createdBy: createdByEnum().notNull().default("system"), // Indicates how the wallet was created
  updatedAt: timestamp()
    .default(sql`CURRENT_TIMESTAMP`)
    .$onUpdate(() => new Date()),
  createdAt: timestamp().default(sql`CURRENT_TIMESTAMP`)
})
