/**
 * Models index file
 *
 * This file exports all model classes for easy importing throughout the application.
 * Each model provides methods for interacting with the corresponding database table.
 */

// Export all model classes
export { User } from "./users"
export { Wallet } from "./wallets"
export { TradingHistory } from "./trading_history"
export { Fees } from "./fees"

// Re-export commonly used types from schema files
export type { TypeUserStats } from "../db/schema/users"
export { UserRole } from "../db/schema/users"
