import { and, desc, eq } from "drizzle-orm"
import { db } from "../db"
import { tradingHistory } from "../db/schema/trading_history"
import { log } from "../log"
import type { TypeChainName } from "../data"

/**
 * Trading History model class for managing trading records
 *
 * This class provides methods for creating, retrieving, and managing trading history
 * records in the PostgreSQL database. Each record represents a trading operation
 * performed by a wallet on a specific blockchain network.
 */
export class TradingHistory {
  /**
   * Create a new trading history record
   * @param walletId Wallet ID that performed the trade
   * @param chain Blockchain network where the trade occurred
   * @param success Whether the trade was successful
   * @param amount Trade amount in the smallest unit (e.g., lamports for Solana)
   * @param operation Trade operation ("B" for buy, "S" for sell)
   * @param closedAt Optional timestamp when the trade was closed
   * @returns The created trading record or null if creation failed
   */
  static async create(walletId: bigint, chain: TypeChainName, success: boolean, amount: bigint, operation: "B" | "S", closedAt?: Date) {
    try {
      await db
        .insert(tradingHistory)
        .values({
          walletId,
          chain,
          success,
          amount,
          operation,
          closedAt: closedAt || new Date()
        })
        .execute()

      // Get the created record (latest for this wallet)
      return this.getLatestForWallet(walletId)
    } catch (error) {
      log.error(`Failed to create trading history: ${error}`)
      return null
    }
  }

  /**
   * Get a trading record by ID
   * @param id Trading record ID
   * @returns The trading record or null if not found
   */
  static async getById(id: number) {
    try {
      const record = await db.select().from(tradingHistory).where(eq(tradingHistory.id, id)).limit(1)
      return record[0] || null
    } catch (error) {
      log.error(`Failed to get trading record by ID: ${error}`)
      return null
    }
  }

  /**
   * Get all trading records for a specific wallet
   * @param walletId Wallet ID
   * @param limit Maximum number of records to return (default: 100)
   * @returns Array of trading records
   */
  static async getForWallet(walletId: bigint, limit: number = 100) {
    try {
      return await db.select().from(tradingHistory).where(eq(tradingHistory.walletId, walletId)).orderBy(desc(tradingHistory.createdAt)).limit(limit)
    } catch (error) {
      log.error(`Failed to get trading records for wallet: ${error}`)
      return []
    }
  }

  /**
   * Get the latest trading record for a wallet
   * @param walletId Wallet ID
   * @returns The latest trading record or null if not found
   */
  static async getLatestForWallet(walletId: bigint) {
    try {
      const record = await db.select().from(tradingHistory).where(eq(tradingHistory.walletId, walletId)).orderBy(desc(tradingHistory.createdAt)).limit(1)
      return record[0] || null
    } catch (error) {
      log.error(`Failed to get latest trading record for wallet: ${error}`)
      return null
    }
  }

  /**
   * Get trading records by chain
   * @param chain Blockchain network
   * @param limit Maximum number of records to return (default: 100)
   * @returns Array of trading records
   */
  static async getByChain(chain: TypeChainName, limit: number = 100) {
    try {
      return await db.select().from(tradingHistory).where(eq(tradingHistory.chain, chain)).orderBy(desc(tradingHistory.createdAt)).limit(limit)
    } catch (error) {
      log.error(`Failed to get trading records by chain: ${error}`)
      return []
    }
  }

  /**
   * Get successful trading records for a wallet
   * @param walletId Wallet ID
   * @param limit Maximum number of records to return (default: 50)
   * @returns Array of successful trading records
   */
  static async getSuccessfulForWallet(walletId: bigint, limit: number = 50) {
    try {
      return await db
        .select()
        .from(tradingHistory)
        .where(and(eq(tradingHistory.walletId, walletId), eq(tradingHistory.success, true)))
        .orderBy(desc(tradingHistory.createdAt))
        .limit(limit)
    } catch (error) {
      log.error(`Failed to get successful trading records for wallet: ${error}`)
      return []
    }
  }

  /**
   * Get failed trading records for a wallet
   * @param walletId Wallet ID
   * @param limit Maximum number of records to return (default: 50)
   * @returns Array of failed trading records
   */
  static async getFailedForWallet(walletId: bigint, limit: number = 50) {
    try {
      return await db
        .select()
        .from(tradingHistory)
        .where(and(eq(tradingHistory.walletId, walletId), eq(tradingHistory.success, false)))
        .orderBy(desc(tradingHistory.createdAt))
        .limit(limit)
    } catch (error) {
      log.error(`Failed to get failed trading records for wallet: ${error}`)
      return []
    }
  }

  /**
   * Calculate trading statistics for a wallet
   * @param walletId Wallet ID
   * @returns Trading statistics object or null if calculation failed
   */
  static async getWalletStats(walletId: bigint) {
    try {
      const records = await this.getForWallet(walletId, 1000) // Get more records for accurate stats

      const stats = {
        totalTrades: records.length,
        successTrades: records.filter((r) => r.success).length,
        failedTrades: records.filter((r) => !r.success).length,
        totalVolume: records.reduce((sum, r) => sum + r.amount, BigInt(0)),
        successVolume: records.filter((r) => r.success).reduce((sum, r) => sum + r.amount, BigInt(0)),
        buyTrades: records.filter((r) => r.operation === "B").length,
        sellTrades: records.filter((r) => r.operation === "S").length
      }

      return stats
    } catch (error) {
      log.error(`Failed to calculate wallet trading stats: ${error}`)
      return null
    }
  }

  /**
   * Calculate trading statistics for a chain
   * @param chain Blockchain network
   * @returns Trading statistics object or null if calculation failed
   */
  static async getChainStats(chain: TypeChainName) {
    try {
      const records = await this.getByChain(chain, 10000) // Get more records for accurate stats

      const stats = {
        totalTrades: records.length,
        successTrades: records.filter((r) => r.success).length,
        failedTrades: records.filter((r) => !r.success).length,
        totalVolume: records.reduce((sum, r) => sum + r.amount, BigInt(0)),
        successVolume: records.filter((r) => r.success).reduce((sum, r) => sum + r.amount, BigInt(0)),
        buyTrades: records.filter((r) => r.operation === "B").length,
        sellTrades: records.filter((r) => r.operation === "S").length,
        uniqueWallets: new Set(records.map((r) => r.walletId.toString())).size
      }

      return stats
    } catch (error) {
      log.error(`Failed to calculate chain trading stats: ${error}`)
      return null
    }
  }

  /**
   * Delete trading records for a wallet (used when wallet is deleted)
   * @param walletId Wallet ID
   * @returns True if deletion was successful, false otherwise
   */
  static async deleteForWallet(walletId: bigint) {
    try {
      await db.delete(tradingHistory).where(eq(tradingHistory.walletId, walletId))
      return true
    } catch (error) {
      log.error(`Failed to delete trading records for wallet: ${error}`)
      return false
    }
  }
}
