import { LoggingPretty } from "logging-pretty"

class W extends LoggingPretty {
  constructor() {
    super({
      pathFile: `./activity.log`
    })
  }

  _writeLogToFile(logData: Parameters<LoggingPretty["_writeLogToFile"]>[0]): void {
    if (logData.strTag == `ERROR`) {
      super._writeLogToFile(logData)
    }
  }
}

export const log = (() => {
  //   if (Bun.isMainThread === false && process.argv[2] == `log`) {
  //     new WorkerThread()
  //     return {}
  //   } else {
  //     return {
  //     }
  //   }
})()
