// import { db } from "./src/db/index"
import { PGlite } from "@electric-sql/pglite"
import { drizzle as drizzlePgLite } from "drizzle-orm/pglite"

const db = drizzlePgLite(new PGlite(`./db-dev`), {
  // schema: {
  //   users: tableUsers,
  //   wallets: tableWallets,
  //   tradingHistory,
  //   fees: tableFees
  // },
  casing: "snake_case"
})

db.execute(`select version();`).then((res) => {
  console.log(res)
})

console.log(`22`)
